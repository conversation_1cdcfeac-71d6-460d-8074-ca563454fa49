package logic

import (
	"context"
	"errors"

	"user/rpc/internal/svc"
	"user/rpc/user"

	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"
)

type GetUserLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetUserLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserLogic {
	return &GetUserLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取用户信息
func (l *GetUserLogic) GetUser(in *user.GetUserRequest) (*user.UserResponse, error) {
	// todo: add your logic here and delete this line

	// 查询用户是否存在
	res, err := l.svcCtx.UserModel.FindByUsername(in.Username)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, status.Error(404, "用户不存在")
		}
		return nil, status.Error(500, err.Error())
	}

	return &user.UserResponse{
		Id:       res.ID,
		Username: res.Username,
		Email:    res.Email,
		Phone:    res.Phone,
		Code:     200,
		Message:  "获取用户信息成功",
	}, nil
}
