package logic

import (
	"context"

	"dict_item_rpc/dict_item"
	"dict_item_rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDictItemLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDictItemLogic {
	return &GetDictItemLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取字典项详情
func (l *GetDictItemLogic) GetDictItem(in *dict_item.GetDictItemReq) (*dict_item.GetDictItemResp, error) {
	// todo: add your logic here and delete this line

	// 参数验证
	if in.Id <= 0 {
		return &dict_item.GetDictItemResp{
			Item:    nil,
			Message: "字典项ID不能为空",
		}, nil
	}

	// 查询记录
	data, err := l.svcCtx.DictItemModel.FindByID(in.Id)
	if err != nil {
		return &dict_item.GetDictItemResp{
			Item:    nil,
			Message: "查询字典项失败",
		}, nil
	}

	return &dict_item.GetDictItemResp{
		Item: &dict_item.DictItem{
			Id:          data.ID,
			DictId:      data.DictID,
			CategoryId:  data.CategoryID,
			Code:        data.Code,
			Name:        data.Name,
			Status:      data.Status,
			CreatedTime: data.CreatedTime.Format("2006-01-02 15:04:05"),
			UpdatedTime: data.UpdatedTime.Format("2006-01-02 15:04:05"),
		},
		Message: "获取字典项详情成功",
	}, nil
}
