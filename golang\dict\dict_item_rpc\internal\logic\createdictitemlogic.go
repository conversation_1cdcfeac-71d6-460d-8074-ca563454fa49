package logic

import (
	"context"
	"dict_category_rpc/dict_category"
	"dict_rpc/dict"

	"dict_item_rpc/dict_item"
	"dict_item_rpc/internal/svc"
	"dict_item_rpc/model"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictItemLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictItemLogic {
	return &CreateDictItemLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 创建字典项
func (l *CreateDictItemLogic) CreateDictItem(in *dict_item.CreateDictItemReq) (*dict_item.CreateDictItemResp, error) {
	// todo: add your logic here and delete this line

	// 参数校验
	if in.DictId <= 0 {
		return &dict_item.CreateDictItemResp{
			Id:      in.DictId,
			Message: "字典ID不能为空",
		}, nil
	}

	if in.CategoryId <= 0 {
		return &dict_item.CreateDictItemResp{
			Id:      in.CategoryId,
			Message: "字典分类ID不能为空",
		}, nil
	}

	// 检查字典是否存在
	_, err := l.svcCtx.DictRpc.GetDict(l.ctx, &dict.GetDictReq{
		Id: in.DictId,
	})
	if err != nil {
		return &dict_item.CreateDictItemResp{
			Id:      in.DictId,
			Message: "字典不存在,请检查字典ID是否正确",
		}, nil
	}
	// 检查字典分类是否存在
	_, err = l.svcCtx.DictCategoryRpc.GetDictCategory(l.ctx, &dict_category.GetDictCategoryReq{
		Id: in.CategoryId,
	})
	if err != nil {
		return &dict_item.CreateDictItemResp{
			Id:      in.CategoryId,
			Message: "字典分类不存在,请检查字典分类ID是否正确",
		}, nil
	}

	// 插入数据
	err = l.svcCtx.DictItemModel.Create(&model.DictItem{
		DictID:     in.DictId,
		CategoryID: in.CategoryId,
		Code:       in.Code,
		Name:       in.Name,
		Status:     in.Status,
	})
	if err != nil {
		return &dict_item.CreateDictItemResp{
			Id:      0,
			Message: "创建字典项失败",
		}, nil
	}
	// 查询记录 -- 使用gorm的方法获取最后一条记录
	data, err := l.svcCtx.DictItemModel.GetLast()

	if err != nil {
		return &dict_item.CreateDictItemResp{
			Id:      0,
			Message: "查询字典项失败",
		}, nil
	}

	return &dict_item.CreateDictItemResp{
		Id:      data.ID,
		Message: "创建字典项成功",
	}, nil
}
