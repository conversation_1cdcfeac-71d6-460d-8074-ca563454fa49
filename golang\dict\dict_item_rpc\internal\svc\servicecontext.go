package svc

import (
	"dict_item_rpc/internal/config"
	"dict_item_rpc/model"
	"dict_category_rpc/dictcategoryservice"
	"dict_rpc/dictservice"
	"github.com/zeromicro/go-zero/zrpc"
)

type ServiceContext struct {
	Config          config.Config
	DictRpc         dictservice.DictService
	DictCategoryRpc dictcategoryservice.DictCategoryService
	DictItemModel   *model.DictItemModel
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config:          c,
		DictRpc:         dictservice.NewDictService(zrpc.MustNewClient(c.DictRpc)),
		DictCategoryRpc: dictcategoryservice.NewDictCategoryService(zrpc.MustNewClient(c.DictCategoryRpc)),
		DictItemModel:   model.NewDictItemModel(model.NewDb(c.Mysql.DataSource)),
	}
}
