package logic

import (
	"context"
	"fmt"

	"user/api/internal/svc"
	"user/api/internal/types"
	"user/rpc/user"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetUserLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserLogic {
	return &GetUserLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserLogic) GetUser(req *types.GetUserRequest) (resp *types.UserResponse, err error) {
	// todo: add your logic here and delete this line

	// 获取JWT token 里面的 Claims设置的username
	username, ok := l.ctx.Value("username").(string)

	if !ok {
		return nil, fmt.Errorf("获取、解析token信息失败: %v", err)
	}

	respRpc, err := l.svcCtx.UserRpc.GetUser(l.ctx, &user.GetUserRequest{
		Username: username,
	})

	return &types.UserResponse{
		Code:    int(respRpc.Code),
		Message: resp.Message,
		BaseResponse: types.BaseResponse{
			Id:       respRpc.Id,
			Username: respRpc.Username,
			Email:    respRpc.Email,
			Phone:    respRpc.Phone,
		},
	}, nil
}
