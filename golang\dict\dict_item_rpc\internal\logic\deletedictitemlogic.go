package logic

import (
	"context"

	"dict_item_rpc/dict_item"
	"dict_item_rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDictItemLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictItemLogic {
	return &DeleteDictItemLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 删除字典项
func (l *DeleteDictItemLogic) DeleteDictItem(in *dict_item.DeleteDictItemReq) (*dict_item.DeleteDictItemResp, error) {
	// todo: add your logic here and delete this line
	// 参数校验
	if in.Id <= 0 {
		return &dict_item.DeleteDictItemResp{
			Success: false,
			Message: "字典项ID不能为空",
		}, nil
	}

	// 删除字典项
	err := l.svcCtx.DictItemModel.Delete(in.Id)

	if err != nil {
		return &dict_item.DeleteDictItemResp{
			Success: false,
			Message: "删除字典项失败",
		}, nil
	}

	return &dict_item.DeleteDictItemResp{
		Success: true,
		Message: "删除字典项成功",
	}, nil
}
