<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="d138aead-4145-4a53-b3ca-b6b8275df4d3" name="更改" comment="download github 第一次提交">
      <change beforePath="$PROJECT_DIR$/golang/dict/dict_category_rpc/internal/config/config.go" beforeDir="false" afterPath="$PROJECT_DIR$/golang/dict/dict_category_rpc/internal/config/config.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/golang/dict/dict_item_rpc/etc/dictitem.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/golang/dict/dict_item_rpc/etc/dictitem.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/golang/dict/dict_item_rpc/go.mod" beforeDir="false" afterPath="$PROJECT_DIR$/golang/dict/dict_item_rpc/go.mod" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/golang/dict/dict_item_rpc/internal/config/config.go" beforeDir="false" afterPath="$PROJECT_DIR$/golang/dict/dict_item_rpc/internal/config/config.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/golang/dict/dict_item_rpc/internal/logic/createdictitemlogic.go" beforeDir="false" afterPath="$PROJECT_DIR$/golang/dict/dict_item_rpc/internal/logic/createdictitemlogic.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/golang/dict/dict_item_rpc/internal/logic/deletedictitemlogic.go" beforeDir="false" afterPath="$PROJECT_DIR$/golang/dict/dict_item_rpc/internal/logic/deletedictitemlogic.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/golang/dict/dict_item_rpc/internal/logic/getdictitemlogic.go" beforeDir="false" afterPath="$PROJECT_DIR$/golang/dict/dict_item_rpc/internal/logic/getdictitemlogic.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/golang/dict/dict_item_rpc/internal/logic/updatedictitemlogic.go" beforeDir="false" afterPath="$PROJECT_DIR$/golang/dict/dict_item_rpc/internal/logic/updatedictitemlogic.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/golang/dict/dict_item_rpc/internal/svc/servicecontext.go" beforeDir="false" afterPath="$PROJECT_DIR$/golang/dict/dict_item_rpc/internal/svc/servicecontext.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/golang/dict/dict_item_rpc/model/dict_item.go" beforeDir="false" afterPath="$PROJECT_DIR$/golang/dict/dict_item_rpc/model/dict_item.go" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Go File" />
      </list>
    </option>
  </component>
  <component name="GOROOT" url="file://$PROJECT_DIR$/../../../golangCodes/pkg/mod/golang.org/<EMAIL>-amd64" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2zr32PGtWs0b7DOZHS6Yp74UeOa" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;DefaultGoTemplateProperty&quot;: &quot;Go File&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.formatter.settings.were.checked&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.migrated.go.modules.settings&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.modules.go.list.on.any.changes.was.set&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;go.import.settings.migrated&quot;: &quot;true&quot;,
    &quot;go.sdk.automatically.set&quot;: &quot;true&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/golang-docker-compose-main&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;vcs.Git&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\src\golang-docker-compose-main\golang" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\golang-docker-compose-main\golang\user\rpc" />
      <recent name="C:\Users\<USER>\golang-docker-compose-main\golang\mp\rpc" />
      <recent name="C:\Users\<USER>\golang-docker-compose-main\golang\mp\api" />
      <recent name="C:\Users\<USER>\golang-docker-compose-main\golang\mp" />
      <recent name="C:\Users\<USER>\golang-docker-compose-main\golang\dict\api\internal\middleware\Cros" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-gosdk-3b128438d3f6-07d2d2d66b1e-org.jetbrains.plugins.go.sharedIndexes.bundled-GO-251.27812.54" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-GO-251.27812.54" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="d138aead-4145-4a53-b3ca-b6b8275df4d3" name="更改" comment="" />
      <created>1752475158085</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752475158085</updated>
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="download github 第一次提交" />
    <option name="LAST_COMMIT_MESSAGE" value="download github 第一次提交" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
</project>